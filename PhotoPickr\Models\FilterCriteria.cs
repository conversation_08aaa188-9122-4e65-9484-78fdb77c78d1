namespace PhotoPickr.Models;

public class FilterCriteria
{
    public bool ShowSelected { get; set; } = true;
    public bool ShowUnselected { get; set; } = true;
    public bool ShowBlurry { get; set; } = true;
    public bool ShowDuplicates { get; set; } = true;
    public bool ShowWithFaces { get; set; } = true;
    public bool ShowWithSmiles { get; set; } = true;
    public bool ShowWithOpenEyes { get; set; } = true;
    
    public int MinRating { get; set; } = 0;
    public int MaxRating { get; set; } = 5;
    
    public DateTime? DateFrom { get; set; }
    public DateTime? DateTo { get; set; }
    
    public int? MinWidth { get; set; }
    public int? MaxWidth { get; set; }
    public int? MinHeight { get; set; }
    public int? MaxHeight { get; set; }
    
    public long? MinFileSize { get; set; }
    public long? MaxFileSize { get; set; }
    
    public string? CameraMake { get; set; }
    public string? CameraModel { get; set; }
    
    public List<string> FileExtensions { get; set; } = new();
    public List<string> Tags { get; set; } = new();
    
    public string? SearchText { get; set; }
    
    public SortBy SortBy { get; set; } = SortBy.DateTaken;
    public SortDirection SortDirection { get; set; } = SortDirection.Descending;
}

public enum SortBy
{
    FileName,
    DateTaken,
    DateAdded,
    FileSize,
    Rating,
    Width,
    Height,
    CameraMake,
    CameraModel
}

public enum SortDirection
{
    Ascending,
    Descending
}
