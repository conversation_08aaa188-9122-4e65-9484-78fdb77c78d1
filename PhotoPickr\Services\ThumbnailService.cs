using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;

namespace PhotoPickr.Services;

public interface IThumbnailService
{
    Task<string?> GenerateThumbnailAsync(string imagePath);
    Task<bool> DeleteThumbnailAsync(string thumbnailPath);
    string GetThumbnailPath(string imagePath);
    Task<bool> RegenerateThumbnailAsync(string imagePath);
}

public class ThumbnailService : IThumbnailService
{
    private readonly ILogger<ThumbnailService> _logger;
    private readonly string _thumbnailDirectory;
    private const int ThumbnailSize = 300;
    private const int ThumbnailQuality = 85;
    
    public ThumbnailService(ILogger<ThumbnailService> logger, IWebHostEnvironment environment)
    {
        _logger = logger;
        _thumbnailDirectory = Path.Combine(environment.ContentRootPath, "wwwroot", "thumbnails");
        
        // Ensure thumbnail directory exists
        if (!Directory.Exists(_thumbnailDirectory))
        {
            Directory.CreateDirectory(_thumbnailDirectory);
        }
    }
    
    public async Task<string?> GenerateThumbnailAsync(string imagePath)
    {
        try
        {
            if (!File.Exists(imagePath))
            {
                _logger.LogWarning("Image file not found: {ImagePath}", imagePath);
                return null;
            }
            
            var thumbnailPath = GetThumbnailPath(imagePath);
            
            // Check if thumbnail already exists and is newer than the original
            if (File.Exists(thumbnailPath))
            {
                var originalModified = File.GetLastWriteTime(imagePath);
                var thumbnailModified = File.GetLastWriteTime(thumbnailPath);
                
                if (thumbnailModified >= originalModified)
                {
                    return thumbnailPath;
                }
            }
            
            // Ensure thumbnail directory exists
            var thumbnailDir = Path.GetDirectoryName(thumbnailPath);
            if (!Directory.Exists(thumbnailDir))
            {
                Directory.CreateDirectory(thumbnailDir!);
            }
            
            using var image = await Image.LoadAsync(imagePath);
            
            // Calculate thumbnail dimensions maintaining aspect ratio
            var (width, height) = CalculateThumbnailDimensions(image.Width, image.Height);
            
            // Resize and save thumbnail
            image.Mutate(x => x.Resize(width, height));
            
            var encoder = new JpegEncoder
            {
                Quality = ThumbnailQuality
            };
            
            await image.SaveAsync(thumbnailPath, encoder);
            
            _logger.LogDebug("Generated thumbnail: {ThumbnailPath}", thumbnailPath);
            return thumbnailPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating thumbnail for: {ImagePath}", imagePath);
            return null;
        }
    }
    
    public async Task<bool> DeleteThumbnailAsync(string thumbnailPath)
    {
        try
        {
            if (File.Exists(thumbnailPath))
            {
                await Task.Run(() => File.Delete(thumbnailPath));
                _logger.LogDebug("Deleted thumbnail: {ThumbnailPath}", thumbnailPath);
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting thumbnail: {ThumbnailPath}", thumbnailPath);
            return false;
        }
    }
    
    public string GetThumbnailPath(string imagePath)
    {
        // Create a unique filename based on the original file path
        var hash = GetFileHash(imagePath);
        var extension = ".jpg"; // Always save thumbnails as JPEG
        var fileName = $"{hash}{extension}";
        
        // Create subdirectory based on first two characters of hash for better file distribution
        var subDir = hash.Substring(0, 2);
        var thumbnailDir = Path.Combine(_thumbnailDirectory, subDir);
        
        return Path.Combine(thumbnailDir, fileName);
    }
    
    public async Task<bool> RegenerateThumbnailAsync(string imagePath)
    {
        try
        {
            var thumbnailPath = GetThumbnailPath(imagePath);
            
            // Delete existing thumbnail
            if (File.Exists(thumbnailPath))
            {
                await DeleteThumbnailAsync(thumbnailPath);
            }
            
            // Generate new thumbnail
            var newThumbnailPath = await GenerateThumbnailAsync(imagePath);
            return !string.IsNullOrEmpty(newThumbnailPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error regenerating thumbnail for: {ImagePath}", imagePath);
            return false;
        }
    }
    
    private (int width, int height) CalculateThumbnailDimensions(int originalWidth, int originalHeight)
    {
        var aspectRatio = (double)originalWidth / originalHeight;
        
        int width, height;
        
        if (aspectRatio > 1) // Landscape
        {
            width = ThumbnailSize;
            height = (int)(ThumbnailSize / aspectRatio);
        }
        else // Portrait or square
        {
            height = ThumbnailSize;
            width = (int)(ThumbnailSize * aspectRatio);
        }
        
        return (width, height);
    }
    
    private string GetFileHash(string filePath)
    {
        // Create a hash based on the file path for consistent thumbnail naming
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(filePath));
        return Convert.ToHexString(hashBytes)[..16]; // Use first 16 characters
    }
}
