/* AUTO-GENERATED FILE. DO NOT MODIFY.
 *
 * This class was automatically generated by
 * .NET for Android from the resource data it found.
 * It should not be modified by hand.
 */
package androidx.legacy.coreutils;

public final class R {
	public static final class attr {
		public static final int alpha = 0x7f030031;
		public static final int font = 0x7f03020d;
		public static final int fontProviderAuthority = 0x7f03020f;
		public static final int fontProviderCerts = 0x7f030210;
		public static final int fontProviderFetchStrategy = 0x7f030211;
		public static final int fontProviderFetchTimeout = 0x7f030212;
		public static final int fontProviderPackage = 0x7f030213;
		public static final int fontProviderQuery = 0x7f030214;
		public static final int fontStyle = 0x7f030216;
		public static final int fontVariationSettings = 0x7f030217;
		public static final int fontWeight = 0x7f030218;
		public static final int ttcIndex = 0x7f0304e7;
	}
	public static final class color {
		public static final int notification_action_color_filter = 0x7f0502f3;
		public static final int notification_icon_bg_color = 0x7f0502f4;
		public static final int ripple_material_light = 0x7f0502fe;
		public static final int secondary_text_default_material_light = 0x7f050300;
	}
	public static final class dimen {
		public static final int compat_button_inset_horizontal_material = 0x7f060058;
		public static final int compat_button_inset_vertical_material = 0x7f060059;
		public static final int compat_button_padding_horizontal_material = 0x7f06005a;
		public static final int compat_button_padding_vertical_material = 0x7f06005b;
		public static final int compat_control_corner_material = 0x7f06005c;
		public static final int compat_notification_large_icon_max_height = 0x7f06005d;
		public static final int compat_notification_large_icon_max_width = 0x7f06005e;
		public static final int notification_action_icon_size = 0x7f060307;
		public static final int notification_action_text_size = 0x7f060308;
		public static final int notification_big_circle_margin = 0x7f060309;
		public static final int notification_content_margin_start = 0x7f06030a;
		public static final int notification_large_icon_height = 0x7f06030b;
		public static final int notification_large_icon_width = 0x7f06030c;
		public static final int notification_main_column_padding_top = 0x7f06030d;
		public static final int notification_media_narrow_margin = 0x7f06030e;
		public static final int notification_right_icon_size = 0x7f06030f;
		public static final int notification_right_side_padding_top = 0x7f060310;
		public static final int notification_small_icon_background_padding = 0x7f060311;
		public static final int notification_small_icon_size_as_large = 0x7f060312;
		public static final int notification_subtext_size = 0x7f060313;
		public static final int notification_top_pad = 0x7f060314;
		public static final int notification_top_pad_large_text = 0x7f060315;
	}
	public static final class drawable {
		public static final int notification_action_background = 0x7f0700d6;
		public static final int notification_bg = 0x7f0700d7;
		public static final int notification_bg_low = 0x7f0700d8;
		public static final int notification_bg_low_normal = 0x7f0700d9;
		public static final int notification_bg_low_pressed = 0x7f0700da;
		public static final int notification_bg_normal = 0x7f0700db;
		public static final int notification_bg_normal_pressed = 0x7f0700dc;
		public static final int notification_icon_background = 0x7f0700dd;
		public static final int notification_template_icon_bg = 0x7f0700df;
		public static final int notification_template_icon_low_bg = 0x7f0700e0;
		public static final int notification_tile_bg = 0x7f0700e1;
		public static final int notify_panel_notification_icon_bg = 0x7f0700e2;
	}
	public static final class id {
		public static final int action_container = 0x7f08003a;
		public static final int action_divider = 0x7f08003c;
		public static final int action_image = 0x7f08003d;
		public static final int action_text = 0x7f080043;
		public static final int actions = 0x7f080044;
		public static final int async = 0x7f080056;
		public static final int blocking = 0x7f080060;
		public static final int chronometer = 0x7f08007b;
		public static final int forever = 0x7f0800ce;
		public static final int icon = 0x7f0800e2;
		public static final int icon_group = 0x7f0800e3;
		public static final int info = 0x7f0800eb;
		public static final int italic = 0x7f0800ef;
		public static final int line1 = 0x7f0800f8;
		public static final int line3 = 0x7f0800f9;
		public static final int normal = 0x7f080150;
		public static final int notification_background = 0x7f080152;
		public static final int notification_main_column = 0x7f080153;
		public static final int notification_main_column_container = 0x7f080154;
		public static final int right_icon = 0x7f080182;
		public static final int right_side = 0x7f080183;
		public static final int tag_transition_group = 0x7f0801d1;
		public static final int tag_unhandled_key_event_manager = 0x7f0801d2;
		public static final int tag_unhandled_key_listeners = 0x7f0801d3;
		public static final int text = 0x7f0801d5;
		public static final int text2 = 0x7f0801d6;
		public static final int time = 0x7f0801e5;
		public static final int title = 0x7f0801e6;
	}
	public static final class integer {
		public static final int status_bar_notification_info_maxnum = 0x7f090044;
	}
	public static final class layout {
		public static final int notification_action = 0x7f0b0067;
		public static final int notification_action_tombstone = 0x7f0b0068;
		public static final int notification_template_custom_big = 0x7f0b0069;
		public static final int notification_template_icon_group = 0x7f0b006a;
		public static final int notification_template_part_chronometer = 0x7f0b006b;
		public static final int notification_template_part_time = 0x7f0b006c;
	}
	public static final class string {
		public static final int status_bar_notification_info_overflow = 0x7f0f00ae;
	}
	public static final class style {
		public static final int TextAppearance_Compat_Notification = 0x7f1001d0;
		public static final int TextAppearance_Compat_Notification_Info = 0x7f1001d1;
		public static final int TextAppearance_Compat_Notification_Line2 = 0x7f1001d2;
		public static final int TextAppearance_Compat_Notification_Time = 0x7f1001d3;
		public static final int TextAppearance_Compat_Notification_Title = 0x7f1001d4;
		public static final int Widget_Compat_NotificationActionContainer = 0x7f100339;
		public static final int Widget_Compat_NotificationActionText = 0x7f10033a;
	}
	public static final class styleable {
		public static final int[] ColorStateListItem = new int[] { 0x010101a5, 0x0101031f, 0x01010647, 0x7f030031, 0x7f030276 };
		public static final int ColorStateListItem_alpha = 3;
		public static final int ColorStateListItem_android_alpha = 1;
		public static final int ColorStateListItem_android_color = 0;
		public static final int[] FontFamily = new int[] { 0x7f03020f, 0x7f030210, 0x7f030211, 0x7f030212, 0x7f030213, 0x7f030214, 0x7f030215 };
		public static final int FontFamily_fontProviderAuthority = 0;
		public static final int FontFamily_fontProviderCerts = 1;
		public static final int FontFamily_fontProviderFetchStrategy = 2;
		public static final int FontFamily_fontProviderFetchTimeout = 3;
		public static final int FontFamily_fontProviderPackage = 4;
		public static final int FontFamily_fontProviderQuery = 5;
		public static final int[] FontFamilyFont = new int[] { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f03020d, 0x7f030216, 0x7f030217, 0x7f030218, 0x7f0304e7 };
		public static final int FontFamilyFont_android_font = 0;
		public static final int FontFamilyFont_android_fontStyle = 2;
		public static final int FontFamilyFont_android_fontVariationSettings = 4;
		public static final int FontFamilyFont_android_fontWeight = 1;
		public static final int FontFamilyFont_android_ttcIndex = 3;
		public static final int FontFamilyFont_font = 5;
		public static final int FontFamilyFont_fontStyle = 6;
		public static final int FontFamilyFont_fontVariationSettings = 7;
		public static final int FontFamilyFont_fontWeight = 8;
		public static final int FontFamilyFont_ttcIndex = 9;
		public static final int[] GradientColor = new int[] { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 };
		public static final int GradientColor_android_centerColor = 7;
		public static final int GradientColor_android_centerX = 3;
		public static final int GradientColor_android_centerY = 4;
		public static final int GradientColor_android_endColor = 1;
		public static final int GradientColor_android_endX = 10;
		public static final int GradientColor_android_endY = 11;
		public static final int GradientColor_android_gradientRadius = 5;
		public static final int GradientColor_android_startColor = 0;
		public static final int GradientColor_android_startX = 8;
		public static final int GradientColor_android_startY = 9;
		public static final int GradientColor_android_tileMode = 6;
		public static final int GradientColor_android_type = 2;
		public static final int[] GradientColorItem = new int[] { 0x010101a5, 0x01010514 };
		public static final int GradientColorItem_android_color = 0;
		public static final int GradientColorItem_android_offset = 1;
	}
}
