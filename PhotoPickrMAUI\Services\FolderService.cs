using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PhotoPickrMAUI.Data;
using PhotoPickrMAUI.Models;

namespace PhotoPickrMAUI.Services;

public interface IFolderService
{
    Task<List<PhotoFolder>> GetAllFoldersAsync();
    Task<PhotoFolder?> GetFolderByIdAsync(int id);
    Task<PhotoFolder> AddFolderAsync(string folderPath, bool isRecursive = true);
    Task<bool> RemoveFolderAsync(int id);
    Task<bool> UpdateFolderAsync(PhotoFolder folder);
    Task<List<string>> GetSubfoldersAsync(string folderPath);
    bool IsValidImageFolder(string folderPath);
}

public class FolderService : IFolderService
{
    private readonly PhotoPickrContext _context;
    private readonly ILogger<FolderService> _logger;
    
    private static readonly string[] ImageExtensions = 
    {
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp", ".heic", ".raw", ".cr2", ".nef", ".arw"
    };
    
    public FolderService(PhotoPickrContext context, ILogger<FolderService> logger)
    {
        _context = context;
        _logger = logger;
    }
    
    public async Task<List<PhotoFolder>> GetAllFoldersAsync()
    {
        try
        {
            return await _context.PhotoFolders
                .Include(f => f.Photos)
                .OrderBy(f => f.FolderName)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving folders");
            return new List<PhotoFolder>();
        }
    }
    
    public async Task<PhotoFolder?> GetFolderByIdAsync(int id)
    {
        try
        {
            return await _context.PhotoFolders
                .Include(f => f.Photos)
                .FirstOrDefaultAsync(f => f.Id == id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving folder with ID {FolderId}", id);
            return null;
        }
    }
    
    public async Task<PhotoFolder> AddFolderAsync(string folderPath, bool isRecursive = true)
    {
        try
        {
            // Check if folder already exists
            var existingFolder = await _context.PhotoFolders
                .FirstOrDefaultAsync(f => f.FolderPath == folderPath);
            
            if (existingFolder != null)
            {
                existingFolder.IsActive = true;
                existingFolder.IsRecursive = isRecursive;
                await _context.SaveChangesAsync();
                return existingFolder;
            }
            
            var folder = new PhotoFolder
            {
                FolderPath = folderPath,
                FolderName = Path.GetFileName(folderPath) ?? folderPath,
                IsRecursive = isRecursive,
                IsActive = true,
                DateAdded = DateTime.UtcNow,
                LastScanned = DateTime.MinValue
            };
            
            _context.PhotoFolders.Add(folder);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Added folder: {FolderPath}", folderPath);
            return folder;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding folder: {FolderPath}", folderPath);
            throw;
        }
    }
    
    public async Task<bool> RemoveFolderAsync(int id)
    {
        try
        {
            var folder = await _context.PhotoFolders.FindAsync(id);
            if (folder == null) return false;
            
            _context.PhotoFolders.Remove(folder);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Removed folder: {FolderPath}", folder.FolderPath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing folder with ID {FolderId}", id);
            return false;
        }
    }
    
    public async Task<bool> UpdateFolderAsync(PhotoFolder folder)
    {
        try
        {
            _context.PhotoFolders.Update(folder);
            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating folder: {FolderPath}", folder.FolderPath);
            return false;
        }
    }
    
    public async Task<List<string>> GetSubfoldersAsync(string folderPath)
    {
        try
        {
            if (!Directory.Exists(folderPath))
                return new List<string>();
            
            return await Task.Run(() =>
            {
                return Directory.GetDirectories(folderPath, "*", SearchOption.AllDirectories)
                    .Where(IsValidImageFolder)
                    .ToList();
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subfolders for: {FolderPath}", folderPath);
            return new List<string>();
        }
    }
    
    public bool IsValidImageFolder(string folderPath)
    {
        try
        {
            if (!Directory.Exists(folderPath))
                return false;
            
            var files = Directory.GetFiles(folderPath, "*.*", SearchOption.TopDirectoryOnly);
            return files.Any(file => ImageExtensions.Contains(Path.GetExtension(file).ToLowerInvariant()));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if folder contains images: {FolderPath}", folderPath);
            return false;
        }
    }
}
