@string/appbar_scrolling_view_behavior;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\layout\fragment_backstack.xml
@string/appbar_scrolling_view_behavior;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\layout\navigationlayout.xml
adaptive-icon;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon_round.xml
adaptive-icon;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon.xml
alpha;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\anim\enterfromleft.xml
alpha;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\anim\exittoleft.xml
alpha;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_default_enter_anim.xml
alpha;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_default_exit_anim.xml
alpha;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_default_pop_enter_anim.xml
alpha;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_default_pop_exit_anim.xml
androidx.coordinatorlayout.widget.CoordinatorLayout;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\layout\shellcontent.xml
androidx.coordinatorlayout.widget.CoordinatorLayout;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\layout\navigationlayout.xml
androidx.drawerlayout.widget.DrawerLayout;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\layout\drawer_layout.xml
androidx.fragment.app.FragmentContainerView;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\layout\fragment_backstack.xml
androidx.fragment.app.FragmentContainerView;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\layout\navigationlayout.xml
attr;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\values\attr.xml
background;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon_round.xml
background;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon.xml
bitmap;obj\Debug\net9.0-android\resizetizer\sp\drawable-v31\maui_splash_image.xml
bitmap;obj\Debug\net9.0-android\resizetizer\sp\drawable\maui_splash_image.xml
cache-path;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\169\jl\res\xml\microsoft_maui_essentials_fileprovider_file_paths.xml
color;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\values\colors.xml
color;obj\Debug\net9.0-android\res\values\colors.xml
color;obj\Debug\net9.0-android\resizetizer\sp\values\maui_colors.xml
com.google.android.material.appbar.AppBarLayout;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\layout\flyoutcontent.xml
com.google.android.material.appbar.AppBarLayout;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\layout\shellcontent.xml
com.google.android.material.appbar.AppBarLayout;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\layout\navigationlayout.xml
declare-styleable;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\values\attr.xml
external-cache-path;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\169\jl\res\xml\microsoft_maui_essentials_fileprovider_file_paths.xml
external-path;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\169\jl\res\xml\microsoft_maui_essentials_fileprovider_file_paths.xml
foreground;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon_round.xml
foreground;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon.xml
item;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\drawable\maui_splash.xml
item;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\values-v35\styles.xml
item;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\values\styles.xml
item;obj\Debug\net9.0-android\resizetizer\sp\drawable-v31\maui_splash_image.xml
item;obj\Debug\net9.0-android\resizetizer\sp\drawable\maui_splash_image.xml
layer-list;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\drawable\maui_splash_image.xml
layer-list;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\drawable\maui_splash.xml
layer-list;obj\Debug\net9.0-android\resizetizer\sp\drawable-v31\maui_splash_image.xml
layer-list;obj\Debug\net9.0-android\resizetizer\sp\drawable\maui_splash_image.xml
microsoft.maui.controls.platform.compatibility.ShellFlyoutLayout;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\layout\flyoutcontent.xml
monochrome;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon_round.xml
monochrome;obj\Debug\net9.0-android\resizetizer\r\mipmap-anydpi-v26\appicon.xml
paths;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\169\jl\res\xml\microsoft_maui_essentials_fileprovider_file_paths.xml
resources;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\values\strings.xml
resources;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\167\jl\res\values\values.xml
resources;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\values-v35\styles.xml
resources;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\values\attr.xml
resources;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\values\colors.xml
resources;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\values\styles.xml
resources;obj\Debug\net9.0-android\res\values\colors.xml
resources;obj\Debug\net9.0-android\resizetizer\sp\values\maui_colors.xml
set;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\anim\enterfromleft.xml
set;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\anim\enterfromright.xml
set;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\anim\exittoleft.xml
set;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\anim\exittoright.xml
set;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_default_enter_anim.xml
set;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_default_exit_anim.xml
set;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_default_pop_enter_anim.xml
set;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_default_pop_exit_anim.xml
set;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_modal_default_enter_anim.xml
set;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_modal_default_exit_anim.xml
string;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\values\strings.xml
string;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\167\jl\res\values\values.xml
style;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\values-v35\styles.xml
style;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\values\styles.xml
translate;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\anim\enterfromleft.xml
translate;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\anim\enterfromright.xml
translate;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\anim\exittoleft.xml
translate;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\166\jl\res\anim\exittoright.xml
translate;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_default_enter_anim.xml
translate;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_default_exit_anim.xml
translate;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_default_pop_enter_anim.xml
translate;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_default_pop_exit_anim.xml
translate;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_modal_default_enter_anim.xml
translate;C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-android\lp\168\jl\res\anim\nav_modal_default_exit_anim.xml
