using Microsoft.EntityFrameworkCore;
using PhotoPickr.Models;

namespace PhotoPickr.Data;

public class PhotoPickrContext : DbContext
{
    public PhotoPickrContext(DbContextOptions<PhotoPickrContext> options) : base(options)
    {
    }
    
    public DbSet<Photo> Photos { get; set; }
    public DbSet<PhotoFolder> PhotoFolders { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        // Configure Photo entity
        modelBuilder.Entity<Photo>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.FilePath).IsRequired().HasMaxLength(500);
            entity.Property(e => e.FileName).IsRequired().HasMaxLength(255);
            entity.Property(e => e.ThumbnailPath).HasMaxLength(500);
            entity.Property(e => e.CameraModel).HasMaxLength(100);
            entity.Property(e => e.CameraMake).HasMaxLength(100);
            entity.Property(e => e.Orientation).HasMaxLength(50);
            entity.Property(e => e.Tags).HasMaxLength(1000);
            entity.Property(e => e.Notes).HasMaxLength(2000);
            
            entity.HasIndex(e => e.FilePath).IsUnique();
            entity.HasIndex(e => e.DateTaken);
            entity.HasIndex(e => e.IsSelected);
            entity.HasIndex(e => e.Rating);
            entity.HasIndex(e => e.IsBlurry);
            entity.HasIndex(e => e.IsDuplicate);
        });
        
        // Configure PhotoFolder entity
        modelBuilder.Entity<PhotoFolder>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.FolderPath).IsRequired().HasMaxLength(500);
            entity.Property(e => e.FolderName).IsRequired().HasMaxLength(255);
            
            entity.HasIndex(e => e.FolderPath).IsUnique();
            entity.HasIndex(e => e.IsActive);
        });
        
        // Configure relationships
        modelBuilder.Entity<Photo>()
            .HasOne(p => p.PhotoFolder)
            .WithMany(f => f.Photos)
            .HasForeignKey(p => p.PhotoFolderId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
