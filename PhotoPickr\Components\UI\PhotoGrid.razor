@using PhotoPickr.Models
@using PhotoPickr.Services
@inject IPhotoService PhotoService
@inject IJSRuntime JSRuntime

<div class="photo-grid-container">
    @if (photos.Any())
    {
        <div class="photo-grid-header d-flex justify-content-between align-items-center mb-3">
            <div class="photo-count">
                <span class="badge bg-primary">@photos.Count photos</span>
                @if (selectedPhotos.Any())
                {
                    <span class="badge bg-success ms-2">@selectedPhotos.Count selected</span>
                }
            </div>
            <div class="grid-controls">
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-secondary btn-sm" @onclick="() => SetGridSize(150)">
                        <i class="fas fa-th"></i> Small
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" @onclick="() => SetGridSize(200)">
                        <i class="fas fa-th-large"></i> Medium
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" @onclick="() => SetGridSize(300)">
                        <i class="fas fa-square"></i> Large
                    </button>
                </div>
                @if (selectedPhotos.Any())
                {
                    <div class="btn-group ms-2" role="group">
                        <button class="btn btn-outline-primary btn-sm" @onclick="SelectAll">
                            <i class="fas fa-check-square"></i> All
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" @onclick="SelectNone">
                            <i class="fas fa-square"></i> None
                        </button>
                    </div>
                }
            </div>
        </div>
        
        <div class="photo-grid" style="grid-template-columns: repeat(auto-fill, minmax(@(gridSize)px, 1fr));">
            @foreach (var photo in photos)
            {
                <div class="photo-item @(selectedPhotos.Contains(photo.Id) ? "selected" : "")" 
                     @onclick="() => ToggleSelection(photo.Id)"
                     @ondblclick="() => ViewPhoto(photo)">
                    
                    <div class="photo-thumbnail">
                        @if (!string.IsNullOrEmpty(photo.ThumbnailPath) && File.Exists(photo.ThumbnailPath))
                        {
                            <img src="@GetThumbnailUrl(photo.ThumbnailPath)" alt="@photo.DisplayName" loading="lazy" />
                        }
                        else
                        {
                            <div class="thumbnail-placeholder">
                                <i class="fas fa-image"></i>
                            </div>
                        }
                        
                        <div class="photo-overlay">
                            <div class="photo-selection">
                                <input type="checkbox" checked="@selectedPhotos.Contains(photo.Id)" 
                                       @onchange="(e) => SetSelection(photo.Id, (bool)e.Value!)" />
                            </div>
                            
                            <div class="photo-rating">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    var starIndex = i;
                                    <i class="@(starIndex <= photo.Rating ? "fas" : "far") fa-star" 
                                       @onclick="() => SetRating(photo.Id, starIndex)" 
                                       @onclick:stopPropagation="true"></i>
                                }
                            </div>
                            
                            <div class="photo-badges">
                                @if (photo.IsBlurry)
                                {
                                    <span class="badge bg-warning" title="Blurry">
                                        <i class="fas fa-eye-slash"></i>
                                    </span>
                                }
                                @if (photo.IsDuplicate)
                                {
                                    <span class="badge bg-danger" title="Duplicate">
                                        <i class="fas fa-copy"></i>
                                    </span>
                                }
                                @if (photo.HasFaces)
                                {
                                    <span class="badge bg-info" title="Has faces">
                                        <i class="fas fa-user"></i>
                                    </span>
                                }
                                @if (photo.HasSmiles)
                                {
                                    <span class="badge bg-success" title="Has smiles">
                                        <i class="fas fa-smile"></i>
                                    </span>
                                }
                            </div>
                        </div>
                    </div>
                    
                    <div class="photo-info">
                        <div class="photo-name" title="@photo.FileName">@photo.DisplayName</div>
                        <div class="photo-details">
                            <small class="text-muted">
                                @photo.Width×@photo.Height • @photo.FileSizeFormatted
                                @if (photo.DateTaken != DateTime.MinValue)
                                {
                                    <br />@photo.DateTaken.ToString("MMM dd, yyyy")
                                }
                            </small>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center text-muted py-5">
            <i class="fas fa-images fa-3x mb-3"></i>
            <p>No photos found. Select a folder and scan for photos to get started.</p>
        </div>
    }
</div>

@code {
    [Parameter] public List<Photo> Photos { get; set; } = new();
    [Parameter] public EventCallback<Photo> OnPhotoSelected { get; set; }
    [Parameter] public EventCallback<List<int>> OnSelectionChanged { get; set; }
    [Parameter] public EventCallback OnPhotosChanged { get; set; }
    
    private List<Photo> photos = new();
    private HashSet<int> selectedPhotos = new();
    private int gridSize = 200;
    
    protected override void OnParametersSet()
    {
        photos = Photos ?? new List<Photo>();
        StateHasChanged();
    }
    
    private void SetGridSize(int size)
    {
        gridSize = size;
        StateHasChanged();
    }
    
    private async Task ToggleSelection(int photoId)
    {
        if (selectedPhotos.Contains(photoId))
        {
            selectedPhotos.Remove(photoId);
        }
        else
        {
            selectedPhotos.Add(photoId);
        }
        
        await PhotoService.SetPhotoSelectionAsync(photoId, selectedPhotos.Contains(photoId));
        await OnSelectionChanged.InvokeAsync(selectedPhotos.ToList());
        StateHasChanged();
    }
    
    private async Task SetSelection(int photoId, bool isSelected)
    {
        if (isSelected)
        {
            selectedPhotos.Add(photoId);
        }
        else
        {
            selectedPhotos.Remove(photoId);
        }
        
        await PhotoService.SetPhotoSelectionAsync(photoId, isSelected);
        await OnSelectionChanged.InvokeAsync(selectedPhotos.ToList());
        StateHasChanged();
    }
    
    private async Task SetRating(int photoId, int rating)
    {
        try
        {
            await PhotoService.SetPhotoRatingAsync(photoId, rating);
            
            // Update the photo in the local list
            var photo = photos.FirstOrDefault(p => p.Id == photoId);
            if (photo != null)
            {
                photo.Rating = rating;
            }
            
            await OnPhotosChanged.InvokeAsync();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error setting rating: {ex.Message}");
        }
    }
    
    private async Task ViewPhoto(Photo photo)
    {
        await OnPhotoSelected.InvokeAsync(photo);
    }
    
    private async Task SelectAll()
    {
        selectedPhotos.Clear();
        foreach (var photo in photos)
        {
            selectedPhotos.Add(photo.Id);
            await PhotoService.SetPhotoSelectionAsync(photo.Id, true);
        }
        
        await OnSelectionChanged.InvokeAsync(selectedPhotos.ToList());
        StateHasChanged();
    }
    
    private async Task SelectNone()
    {
        foreach (var photoId in selectedPhotos.ToList())
        {
            await PhotoService.SetPhotoSelectionAsync(photoId, false);
        }
        
        selectedPhotos.Clear();
        await OnSelectionChanged.InvokeAsync(selectedPhotos.ToList());
        StateHasChanged();
    }
    
    private string GetThumbnailUrl(string thumbnailPath)
    {
        // Convert absolute path to relative web path
        var webRootPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
        if (thumbnailPath.StartsWith(webRootPath))
        {
            var relativePath = Path.GetRelativePath(webRootPath, thumbnailPath);
            return "/" + relativePath.Replace('\\', '/');
        }
        
        return thumbnailPath;
    }
    
    public void UpdateSelection(List<int> newSelection)
    {
        selectedPhotos = newSelection.ToHashSet();
        StateHasChanged();
    }
}
