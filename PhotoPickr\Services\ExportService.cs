using System.IO.Compression;
using System.Text.Json;
using PhotoPickr.Models;

namespace PhotoPickr.Services;

public interface IExportService
{
    Task<string?> CopySelectedPhotosAsync(List<Photo> photos, string destinationFolder, IProgress<string>? progress = null);
    Task<string?> CreateZipArchiveAsync(List<Photo> photos, string zipFileName, IProgress<string>? progress = null);
    Task<string?> ExportToCsvAsync(List<Photo> photos, string csvFileName);
    Task<string?> ExportToJsonAsync(List<Photo> photos, string jsonFileName);
    Task<bool> CreatePhotoAlbumPdfAsync(List<Photo> photos, string pdfFileName, IProgress<string>? progress = null);
}

public class ExportService : IExportService
{
    private readonly ILogger<ExportService> _logger;
    private readonly IWebHostEnvironment _environment;
    
    public ExportService(ILogger<ExportService> logger, IWebHostEnvironment environment)
    {
        _logger = logger;
        _environment = environment;
    }
    
    public async Task<string?> CopySelectedPhotosAsync(List<Photo> photos, string destinationFolder, IProgress<string>? progress = null)
    {
        try
        {
            if (!Directory.Exists(destinationFolder))
            {
                Directory.CreateDirectory(destinationFolder);
            }
            
            var copiedCount = 0;
            var totalCount = photos.Count;
            
            foreach (var photo in photos)
            {
                if (!File.Exists(photo.FilePath))
                {
                    _logger.LogWarning("Source file not found: {FilePath}", photo.FilePath);
                    continue;
                }
                
                var destinationPath = Path.Combine(destinationFolder, photo.FileName);
                
                // Handle duplicate filenames
                var counter = 1;
                var originalDestinationPath = destinationPath;
                while (File.Exists(destinationPath))
                {
                    var nameWithoutExt = Path.GetFileNameWithoutExtension(originalDestinationPath);
                    var extension = Path.GetExtension(originalDestinationPath);
                    destinationPath = Path.Combine(destinationFolder, $"{nameWithoutExt}_{counter}{extension}");
                    counter++;
                }
                
                await Task.Run(() => File.Copy(photo.FilePath, destinationPath, false));
                copiedCount++;
                
                progress?.Report($"Copied {copiedCount} of {totalCount} photos");
            }
            
            _logger.LogInformation("Copied {CopiedCount} photos to {DestinationFolder}", copiedCount, destinationFolder);
            return destinationFolder;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error copying photos to {DestinationFolder}", destinationFolder);
            return null;
        }
    }
    
    public async Task<string?> CreateZipArchiveAsync(List<Photo> photos, string zipFileName, IProgress<string>? progress = null)
    {
        try
        {
            var tempDir = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
            Directory.CreateDirectory(tempDir);
            
            var zipPath = Path.Combine(_environment.ContentRootPath, "wwwroot", "exports", zipFileName);
            var exportDir = Path.GetDirectoryName(zipPath);
            if (!Directory.Exists(exportDir))
            {
                Directory.CreateDirectory(exportDir!);
            }
            
            var addedCount = 0;
            var totalCount = photos.Count;
            
            using (var archive = ZipFile.Open(zipPath, ZipArchiveMode.Create))
            {
                foreach (var photo in photos)
                {
                    if (!File.Exists(photo.FilePath))
                    {
                        _logger.LogWarning("Source file not found: {FilePath}", photo.FilePath);
                        continue;
                    }
                    
                    var entryName = photo.FileName;
                    
                    // Handle duplicate filenames in zip
                    var counter = 1;
                    var originalEntryName = entryName;
                    while (archive.Entries.Any(e => e.Name == entryName))
                    {
                        var nameWithoutExt = Path.GetFileNameWithoutExtension(originalEntryName);
                        var extension = Path.GetExtension(originalEntryName);
                        entryName = $"{nameWithoutExt}_{counter}{extension}";
                        counter++;
                    }
                    
                    await Task.Run(() => archive.CreateEntryFromFile(photo.FilePath, entryName));
                    addedCount++;
                    
                    progress?.Report($"Added {addedCount} of {totalCount} photos to archive");
                }
            }
            
            // Clean up temp directory
            if (Directory.Exists(tempDir))
            {
                Directory.Delete(tempDir, true);
            }
            
            _logger.LogInformation("Created ZIP archive with {PhotoCount} photos: {ZipPath}", addedCount, zipPath);
            return zipPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating ZIP archive: {ZipFileName}", zipFileName);
            return null;
        }
    }
    
    public async Task<string?> ExportToCsvAsync(List<Photo> photos, string csvFileName)
    {
        try
        {
            var csvPath = Path.Combine(_environment.ContentRootPath, "wwwroot", "exports", csvFileName);
            var exportDir = Path.GetDirectoryName(csvPath);
            if (!Directory.Exists(exportDir))
            {
                Directory.CreateDirectory(exportDir!);
            }
            
            using var writer = new StreamWriter(csvPath);
            
            // Write header
            await writer.WriteLineAsync("FileName,FilePath,FileSize,DateTaken,Width,Height,CameraMake,CameraModel,Rating,IsSelected,Tags,Notes");
            
            // Write data
            foreach (var photo in photos)
            {
                var line = $"\"{photo.FileName}\",\"{photo.FilePath}\",{photo.FileSize},\"{photo.DateTaken:yyyy-MM-dd HH:mm:ss}\"," +
                          $"{photo.Width},{photo.Height},\"{photo.CameraMake}\",\"{photo.CameraModel}\"," +
                          $"{photo.Rating},{photo.IsSelected},\"{photo.Tags}\",\"{photo.Notes}\"";
                
                await writer.WriteLineAsync(line);
            }
            
            _logger.LogInformation("Exported {PhotoCount} photos to CSV: {CsvPath}", photos.Count, csvPath);
            return csvPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting to CSV: {CsvFileName}", csvFileName);
            return null;
        }
    }
    
    public async Task<string?> ExportToJsonAsync(List<Photo> photos, string jsonFileName)
    {
        try
        {
            var jsonPath = Path.Combine(_environment.ContentRootPath, "wwwroot", "exports", jsonFileName);
            var exportDir = Path.GetDirectoryName(jsonPath);
            if (!Directory.Exists(exportDir))
            {
                Directory.CreateDirectory(exportDir!);
            }
            
            var exportData = photos.Select(p => new
            {
                p.FileName,
                p.FilePath,
                p.FileSize,
                p.DateTaken,
                p.Width,
                p.Height,
                p.CameraMake,
                p.CameraModel,
                p.FocalLength,
                p.Aperture,
                p.ShutterSpeed,
                p.Iso,
                p.Rating,
                p.IsSelected,
                p.IsBlurry,
                p.IsDuplicate,
                p.HasFaces,
                p.HasSmiles,
                p.HasOpenEyes,
                p.Tags,
                p.Notes,
                p.Latitude,
                p.Longitude
            }).ToList();
            
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            
            var json = JsonSerializer.Serialize(exportData, options);
            await File.WriteAllTextAsync(jsonPath, json);
            
            _logger.LogInformation("Exported {PhotoCount} photos to JSON: {JsonPath}", photos.Count, jsonPath);
            return jsonPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting to JSON: {JsonFileName}", jsonFileName);
            return null;
        }
    }
    
    public async Task<bool> CreatePhotoAlbumPdfAsync(List<Photo> photos, string pdfFileName, IProgress<string>? progress = null)
    {
        try
        {
            // Note: This is a placeholder implementation
            // For a full PDF implementation, you would need a library like iTextSharp or PdfSharp
            
            var pdfPath = Path.Combine(_environment.ContentRootPath, "wwwroot", "exports", pdfFileName);
            var exportDir = Path.GetDirectoryName(pdfPath);
            if (!Directory.Exists(exportDir))
            {
                Directory.CreateDirectory(exportDir!);
            }
            
            // For now, create a simple HTML file that could be converted to PDF
            var htmlPath = Path.ChangeExtension(pdfPath, ".html");
            
            using var writer = new StreamWriter(htmlPath);
            await writer.WriteLineAsync("<!DOCTYPE html>");
            await writer.WriteLineAsync("<html><head><title>Photo Album</title>");
            await writer.WriteLineAsync("<style>body { font-family: Arial, sans-serif; } .photo { margin: 20px; page-break-inside: avoid; } img { max-width: 100%; height: auto; }</style>");
            await writer.WriteLineAsync("</head><body>");
            await writer.WriteLineAsync("<h1>Photo Album</h1>");
            
            var processedCount = 0;
            foreach (var photo in photos)
            {
                await writer.WriteLineAsync("<div class='photo'>");
                await writer.WriteLineAsync($"<h3>{photo.DisplayName}</h3>");
                await writer.WriteLineAsync($"<p>Date: {photo.DateTaken:yyyy-MM-dd HH:mm:ss}</p>");
                await writer.WriteLineAsync($"<p>Size: {photo.Width}x{photo.Height} ({photo.FileSizeFormatted})</p>");
                if (!string.IsNullOrEmpty(photo.CameraMake) || !string.IsNullOrEmpty(photo.CameraModel))
                {
                    await writer.WriteLineAsync($"<p>Camera: {photo.CameraMake} {photo.CameraModel}</p>");
                }
                await writer.WriteLineAsync($"<p>Rating: {new string('★', photo.Rating)}{new string('☆', 5 - photo.Rating)}</p>");
                await writer.WriteLineAsync("</div>");
                
                processedCount++;
                progress?.Report($"Processed {processedCount} of {photos.Count} photos");
            }
            
            await writer.WriteLineAsync("</body></html>");
            
            _logger.LogInformation("Created photo album HTML: {HtmlPath}", htmlPath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating photo album PDF: {PdfFileName}", pdfFileName);
            return false;
        }
    }
}
