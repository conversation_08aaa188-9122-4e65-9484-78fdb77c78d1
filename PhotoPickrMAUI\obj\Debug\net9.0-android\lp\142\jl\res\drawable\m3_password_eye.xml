<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2016 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
-->

<animated-selector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="NewApi">

    <item
        android:id="@+id/visible"
        android:drawable="@drawable/design_ic_visibility_off"
        android:state_checked="true"/>

    <item
        android:id="@+id/masked"
        android:drawable="@drawable/design_ic_visibility"/>

    <transition
        android:drawable="@drawable/m3_avd_hide_password"
        android:fromId="@id/masked"
        android:toId="@id/visible"/>

    <transition
        android:drawable="@drawable/m3_avd_show_password"
        android:fromId="@id/visible"
        android:toId="@id/masked"/>

</animated-selector>

