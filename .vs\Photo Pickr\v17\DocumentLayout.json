{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\Photo Pickr\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{3E4E38A9-C1C5-ED24-ABB2-2D2EF1D7A112}|PhotoPickr\\PhotoPickr.csproj|c:\\users\\<USER>\\documents\\augment-projects\\photo pickr\\photopickr\\components\\app.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{3E4E38A9-C1C5-ED24-ABB2-2D2EF1D7A112}|PhotoPickr\\PhotoPickr.csproj|solutionrelative:photopickr\\components\\app.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{3E4E38A9-C1C5-ED24-ABB2-2D2EF1D7A112}|PhotoPickr\\PhotoPickr.csproj|c:\\users\\<USER>\\documents\\augment-projects\\photo pickr\\photopickr\\components\\routes.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{3E4E38A9-C1C5-ED24-ABB2-2D2EF1D7A112}|PhotoPickr\\PhotoPickr.csproj|solutionrelative:photopickr\\components\\routes.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{3E4E38A9-C1C5-ED24-ABB2-2D2EF1D7A112}|PhotoPickr\\PhotoPickr.csproj|c:\\users\\<USER>\\documents\\augment-projects\\photo pickr\\photopickr\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3E4E38A9-C1C5-ED24-ABB2-2D2EF1D7A112}|PhotoPickr\\PhotoPickr.csproj|solutionrelative:photopickr\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "App.razor", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\Photo Pickr\\PhotoPickr\\Components\\App.razor", "RelativeDocumentMoniker": "PhotoPickr\\Components\\App.razor", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\Photo Pickr\\PhotoPickr\\Components\\App.razor", "RelativeToolTip": "PhotoPickr\\Components\\App.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-06-12T10:56:03.84Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Routes.razor", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\Photo Pickr\\PhotoPickr\\Components\\Routes.razor", "RelativeDocumentMoniker": "PhotoPickr\\Components\\Routes.razor", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\Photo Pickr\\PhotoPickr\\Components\\Routes.razor", "RelativeToolTip": "PhotoPickr\\Components\\Routes.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-06-12T10:56:00Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Documents\\augment-projects\\Photo Pickr\\PhotoPickr\\Program.cs", "RelativeDocumentMoniker": "PhotoPickr\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Documents\\augment-projects\\Photo Pickr\\PhotoPickr\\Program.cs", "RelativeToolTip": "PhotoPickr\\Program.cs", "ViewState": "AgIAABgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T10:55:41.424Z", "EditorCaption": ""}]}]}]}