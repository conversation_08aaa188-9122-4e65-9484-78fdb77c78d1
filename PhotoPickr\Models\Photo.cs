using System.ComponentModel.DataAnnotations;

namespace PhotoPickr.Models;

public class Photo
{
    public int Id { get; set; }
    
    [Required]
    public string FilePath { get; set; } = string.Empty;
    
    public string FileName { get; set; } = string.Empty;
    
    public string? ThumbnailPath { get; set; }
    
    public long FileSize { get; set; }
    
    public DateTime DateTaken { get; set; }
    
    public DateTime DateAdded { get; set; } = DateTime.UtcNow;
    
    public int Width { get; set; }
    
    public int Height { get; set; }
    
    public string? CameraModel { get; set; }
    
    public string? CameraMake { get; set; }
    
    public double? Latitude { get; set; }
    
    public double? Longitude { get; set; }
    
    public string? Orientation { get; set; }
    
    public double? FocalLength { get; set; }
    
    public double? Aperture { get; set; }
    
    public double? ShutterSpeed { get; set; }
    
    public int? Iso { get; set; }
    
    public bool IsSelected { get; set; }
    
    public int Rating { get; set; } // 0-5 stars
    
    public bool IsBlurry { get; set; }
    
    public bool IsDuplicate { get; set; }
    
    public bool HasFaces { get; set; }
    
    public bool HasSmiles { get; set; }
    
    public bool HasOpenEyes { get; set; }
    
    public string? Tags { get; set; } // JSON array of tags
    
    public string? Notes { get; set; }
    
    // Navigation properties
    public int PhotoFolderId { get; set; }
    public PhotoFolder PhotoFolder { get; set; } = null!;
    
    // Computed properties
    public string DisplayName => Path.GetFileNameWithoutExtension(FileName);
    
    public string FileExtension => Path.GetExtension(FileName).ToLowerInvariant();
    
    public double AspectRatio => Height > 0 ? (double)Width / Height : 1.0;
    
    public string FileSizeFormatted
    {
        get
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = FileSize;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
    
    public bool IsImageFile
    {
        get
        {
            var imageExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp", ".heic", ".raw" };
            return imageExtensions.Contains(FileExtension);
        }
    }
}
