﻿<?xml version="1.0" encoding="utf-8"?><XamlCompilerSaveState><XamlFeatureControlFlags>Nothing</XamlFeatureControlFlags><ReferenceAssemblyList><ReferenceAssembly PathName="c:\users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2792.45\lib_manual\net6.0-windows10.0.17763.0\microsoft.web.webview2.core.projection.dll" HashGuid="d784d0bb-01d0-9bee-97b6-7bb2d999d35a" /></ReferenceAssemblyList><XamlSourceFileDataList><XamlSourceFileData XamlFileName="Platforms\Windows\App.xaml" ClassFullName="PhotoPickrMAUI.WinUI.App" GeneratedCodePathPrefix="C:\Users\<USER>\Documents\augment-projects\Photo Pickr\PhotoPickrMAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\Platforms\Windows\App" XamlFileTimeAtLastCompileInTicks="638853446222781048" HasBoundEventAssignments="False" /></XamlSourceFileDataList></XamlCompilerSaveState>