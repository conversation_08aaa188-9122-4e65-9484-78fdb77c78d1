using Microsoft.EntityFrameworkCore;
using PhotoPickr.Data;
using PhotoPickr.Models;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using MetadataExtractor;
using MetadataExtractor.Formats.Exif;
using Directory = System.IO.Directory;
using File = System.IO.File;

namespace PhotoPickr.Services;

public interface IPhotoService
{
    Task<List<Photo>> GetPhotosAsync(int? folderId = null);
    Task<List<Photo>> GetFilteredPhotosAsync(FilterCriteria criteria);
    Task<Photo?> GetPhotoByIdAsync(int id);
    Task<bool> ScanFolderAsync(int folderId, IProgress<string>? progress = null);
    Task<bool> UpdatePhotoAsync(Photo photo);
    Task<bool> DeletePhotoAsync(int id);
    Task<bool> SetPhotoSelectionAsync(int photoId, bool isSelected);
    Task<bool> SetPhotoRatingAsync(int photoId, int rating);
    Task<int> GetPhotoCountAsync(int? folderId = null);
}

public class PhotoService : IPhotoService
{
    private readonly PhotoPickrContext _context;
    private readonly IThumbnailService _thumbnailService;
    private readonly ILogger<PhotoService> _logger;
    
    private static readonly string[] ImageExtensions = 
    {
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp", ".heic", ".raw", ".cr2", ".nef", ".arw"
    };
    
    public PhotoService(PhotoPickrContext context, IThumbnailService thumbnailService, ILogger<PhotoService> logger)
    {
        _context = context;
        _thumbnailService = thumbnailService;
        _logger = logger;
    }
    
    public async Task<List<Photo>> GetPhotosAsync(int? folderId = null)
    {
        try
        {
            var query = _context.Photos.Include(p => p.PhotoFolder).AsQueryable();
            
            if (folderId.HasValue)
            {
                query = query.Where(p => p.PhotoFolderId == folderId.Value);
            }
            
            return await query.OrderByDescending(p => p.DateTaken).ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving photos");
            return new List<Photo>();
        }
    }
    
    public async Task<List<Photo>> GetFilteredPhotosAsync(FilterCriteria criteria)
    {
        try
        {
            var query = _context.Photos.Include(p => p.PhotoFolder).AsQueryable();
            
            // Apply filters
            if (!criteria.ShowSelected && !criteria.ShowUnselected)
            {
                return new List<Photo>();
            }
            else if (!criteria.ShowSelected)
            {
                query = query.Where(p => !p.IsSelected);
            }
            else if (!criteria.ShowUnselected)
            {
                query = query.Where(p => p.IsSelected);
            }
            
            if (!criteria.ShowBlurry)
                query = query.Where(p => !p.IsBlurry);
            
            if (!criteria.ShowDuplicates)
                query = query.Where(p => !p.IsDuplicate);
            
            if (!criteria.ShowWithFaces)
                query = query.Where(p => !p.HasFaces);
            
            if (!criteria.ShowWithSmiles)
                query = query.Where(p => !p.HasSmiles);
            
            if (!criteria.ShowWithOpenEyes)
                query = query.Where(p => !p.HasOpenEyes);
            
            // Rating filter
            query = query.Where(p => p.Rating >= criteria.MinRating && p.Rating <= criteria.MaxRating);
            
            // Date filter
            if (criteria.DateFrom.HasValue)
                query = query.Where(p => p.DateTaken >= criteria.DateFrom.Value);
            
            if (criteria.DateTo.HasValue)
                query = query.Where(p => p.DateTaken <= criteria.DateTo.Value);
            
            // Dimension filters
            if (criteria.MinWidth.HasValue)
                query = query.Where(p => p.Width >= criteria.MinWidth.Value);
            
            if (criteria.MaxWidth.HasValue)
                query = query.Where(p => p.Width <= criteria.MaxWidth.Value);
            
            if (criteria.MinHeight.HasValue)
                query = query.Where(p => p.Height >= criteria.MinHeight.Value);
            
            if (criteria.MaxHeight.HasValue)
                query = query.Where(p => p.Height <= criteria.MaxHeight.Value);
            
            // File size filters
            if (criteria.MinFileSize.HasValue)
                query = query.Where(p => p.FileSize >= criteria.MinFileSize.Value);
            
            if (criteria.MaxFileSize.HasValue)
                query = query.Where(p => p.FileSize <= criteria.MaxFileSize.Value);
            
            // Camera filters
            if (!string.IsNullOrEmpty(criteria.CameraMake))
                query = query.Where(p => p.CameraMake != null && p.CameraMake.Contains(criteria.CameraMake));
            
            if (!string.IsNullOrEmpty(criteria.CameraModel))
                query = query.Where(p => p.CameraModel != null && p.CameraModel.Contains(criteria.CameraModel));
            
            // File extension filter
            if (criteria.FileExtensions.Any())
            {
                query = query.Where(p => criteria.FileExtensions.Any(ext => p.FileName.EndsWith(ext)));
            }
            
            // Search text filter
            if (!string.IsNullOrEmpty(criteria.SearchText))
            {
                var searchText = criteria.SearchText.ToLower();
                query = query.Where(p => 
                    p.FileName.ToLower().Contains(searchText) ||
                    (p.Notes != null && p.Notes.ToLower().Contains(searchText)) ||
                    (p.Tags != null && p.Tags.ToLower().Contains(searchText)));
            }
            
            // Apply sorting
            query = criteria.SortBy switch
            {
                SortBy.FileName => criteria.SortDirection == SortDirection.Ascending 
                    ? query.OrderBy(p => p.FileName) 
                    : query.OrderByDescending(p => p.FileName),
                SortBy.DateTaken => criteria.SortDirection == SortDirection.Ascending 
                    ? query.OrderBy(p => p.DateTaken) 
                    : query.OrderByDescending(p => p.DateTaken),
                SortBy.DateAdded => criteria.SortDirection == SortDirection.Ascending 
                    ? query.OrderBy(p => p.DateAdded) 
                    : query.OrderByDescending(p => p.DateAdded),
                SortBy.FileSize => criteria.SortDirection == SortDirection.Ascending 
                    ? query.OrderBy(p => p.FileSize) 
                    : query.OrderByDescending(p => p.FileSize),
                SortBy.Rating => criteria.SortDirection == SortDirection.Ascending 
                    ? query.OrderBy(p => p.Rating) 
                    : query.OrderByDescending(p => p.Rating),
                SortBy.Width => criteria.SortDirection == SortDirection.Ascending 
                    ? query.OrderBy(p => p.Width) 
                    : query.OrderByDescending(p => p.Width),
                SortBy.Height => criteria.SortDirection == SortDirection.Ascending 
                    ? query.OrderBy(p => p.Height) 
                    : query.OrderByDescending(p => p.Height),
                SortBy.CameraMake => criteria.SortDirection == SortDirection.Ascending 
                    ? query.OrderBy(p => p.CameraMake) 
                    : query.OrderByDescending(p => p.CameraMake),
                SortBy.CameraModel => criteria.SortDirection == SortDirection.Ascending 
                    ? query.OrderBy(p => p.CameraModel) 
                    : query.OrderByDescending(p => p.CameraModel),
                _ => query.OrderByDescending(p => p.DateTaken)
            };
            
            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error filtering photos");
            return new List<Photo>();
        }
    }
    
    public async Task<Photo?> GetPhotoByIdAsync(int id)
    {
        try
        {
            return await _context.Photos
                .Include(p => p.PhotoFolder)
                .FirstOrDefaultAsync(p => p.Id == id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving photo with ID {PhotoId}", id);
            return null;
        }
    }
    
    public async Task<bool> UpdatePhotoAsync(Photo photo)
    {
        try
        {
            _context.Photos.Update(photo);
            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating photo: {PhotoPath}", photo.FilePath);
            return false;
        }
    }
    
    public async Task<bool> DeletePhotoAsync(int id)
    {
        try
        {
            var photo = await _context.Photos.FindAsync(id);
            if (photo == null) return false;
            
            _context.Photos.Remove(photo);
            await _context.SaveChangesAsync();
            
            // Delete thumbnail if exists
            if (!string.IsNullOrEmpty(photo.ThumbnailPath) && File.Exists(photo.ThumbnailPath))
            {
                File.Delete(photo.ThumbnailPath);
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting photo with ID {PhotoId}", id);
            return false;
        }
    }
    
    public async Task<bool> SetPhotoSelectionAsync(int photoId, bool isSelected)
    {
        try
        {
            var photo = await _context.Photos.FindAsync(photoId);
            if (photo == null) return false;
            
            photo.IsSelected = isSelected;
            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting photo selection for ID {PhotoId}", photoId);
            return false;
        }
    }
    
    public async Task<bool> SetPhotoRatingAsync(int photoId, int rating)
    {
        try
        {
            if (rating < 0 || rating > 5) return false;
            
            var photo = await _context.Photos.FindAsync(photoId);
            if (photo == null) return false;
            
            photo.Rating = rating;
            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting photo rating for ID {PhotoId}", photoId);
            return false;
        }
    }
    
    public async Task<bool> ScanFolderAsync(int folderId, IProgress<string>? progress = null)
    {
        try
        {
            var folder = await _context.PhotoFolders.FindAsync(folderId);
            if (folder == null || !Directory.Exists(folder.FolderPath))
            {
                return false;
            }

            progress?.Report($"Scanning folder: {folder.FolderPath}");

            var searchOption = folder.IsRecursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
            var imageFiles = Directory.GetFiles(folder.FolderPath, "*.*", searchOption)
                .Where(file => ImageExtensions.Contains(Path.GetExtension(file).ToLowerInvariant()))
                .ToList();

            var existingPhotos = await _context.Photos
                .Where(p => p.PhotoFolderId == folderId)
                .Select(p => p.FilePath)
                .ToHashSetAsync();

            int processedCount = 0;
            foreach (var filePath in imageFiles)
            {
                if (existingPhotos.Contains(filePath))
                {
                    continue; // Skip already processed photos
                }

                try
                {
                    var photo = await CreatePhotoFromFileAsync(filePath, folderId);
                    if (photo != null)
                    {
                        _context.Photos.Add(photo);
                        processedCount++;

                        if (processedCount % 10 == 0)
                        {
                            await _context.SaveChangesAsync();
                            progress?.Report($"Processed {processedCount} of {imageFiles.Count} photos");
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error processing photo: {FilePath}", filePath);
                }
            }

            await _context.SaveChangesAsync();

            // Update folder statistics
            folder.PhotoCount = await _context.Photos.CountAsync(p => p.PhotoFolderId == folderId);
            folder.LastScanned = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            progress?.Report($"Scan complete. Processed {processedCount} new photos.");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scanning folder with ID {FolderId}", folderId);
            return false;
        }
    }

    private async Task<Photo?> CreatePhotoFromFileAsync(string filePath, int folderId)
    {
        try
        {
            var fileInfo = new FileInfo(filePath);
            if (!fileInfo.Exists) return null;

            var photo = new Photo
            {
                FilePath = filePath,
                FileName = fileInfo.Name,
                FileSize = fileInfo.Length,
                DateAdded = DateTime.UtcNow,
                PhotoFolderId = folderId
            };

            // Extract metadata
            await ExtractMetadataAsync(photo);

            // Generate thumbnail
            photo.ThumbnailPath = await _thumbnailService.GenerateThumbnailAsync(filePath);

            return photo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating photo from file: {FilePath}", filePath);
            return null;
        }
    }

    private async Task ExtractMetadataAsync(Photo photo)
    {
        try
        {
            var directories = ImageMetadataReader.ReadMetadata(photo.FilePath);

            // Extract basic image dimensions
            using var image = await Image.LoadAsync(photo.FilePath);
            photo.Width = image.Width;
            photo.Height = image.Height;

            // Extract EXIF data
            var exifSubIfdDirectory = directories.OfType<ExifSubIfdDirectory>().FirstOrDefault();
            var exifIfd0Directory = directories.OfType<ExifIfd0Directory>().FirstOrDefault();

            if (exifSubIfdDirectory != null)
            {
                if (exifSubIfdDirectory.TryGetDateTime(ExifDirectoryBase.TagDateTimeOriginal, out var dateTaken))
                {
                    photo.DateTaken = dateTaken;
                }
                else
                {
                    photo.DateTaken = File.GetCreationTime(photo.FilePath);
                }

                if (exifSubIfdDirectory.HasTagName(ExifDirectoryBase.TagFocalLength))
                {
                    if (exifSubIfdDirectory.TryGetDouble(ExifDirectoryBase.TagFocalLength, out var focalLength))
                        photo.FocalLength = focalLength;
                }

                if (exifSubIfdDirectory.HasTagName(ExifDirectoryBase.TagFNumber))
                {
                    if (exifSubIfdDirectory.TryGetDouble(ExifDirectoryBase.TagFNumber, out var aperture))
                        photo.Aperture = aperture;
                }

                if (exifSubIfdDirectory.HasTagName(ExifDirectoryBase.TagExposureTime))
                {
                    if (exifSubIfdDirectory.TryGetDouble(ExifDirectoryBase.TagExposureTime, out var shutterSpeed))
                        photo.ShutterSpeed = shutterSpeed;
                }

                if (exifSubIfdDirectory.HasTagName(ExifDirectoryBase.TagIsoEquivalent))
                {
                    if (exifSubIfdDirectory.TryGetInt32(ExifDirectoryBase.TagIsoEquivalent, out var iso))
                        photo.Iso = iso;
                }
            }
            else
            {
                photo.DateTaken = File.GetCreationTime(photo.FilePath);
            }

            if (exifIfd0Directory != null)
            {
                photo.CameraMake = exifIfd0Directory.GetDescription(ExifDirectoryBase.TagMake);
                photo.CameraModel = exifIfd0Directory.GetDescription(ExifDirectoryBase.TagModel);
                photo.Orientation = exifIfd0Directory.GetDescription(ExifDirectoryBase.TagOrientation);
            }

            // Extract GPS data
            var gpsDirectory = directories.OfType<MetadataExtractor.Formats.Exif.GpsDirectory>().FirstOrDefault();
            if (gpsDirectory != null)
            {
                var location = gpsDirectory.GetGeoLocation();
                if (location != null)
                {
                    photo.Latitude = location.Latitude;
                    photo.Longitude = location.Longitude;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error extracting metadata from: {FilePath}", photo.FilePath);
            photo.DateTaken = File.GetCreationTime(photo.FilePath);
        }
    }

    public async Task<int> GetPhotoCountAsync(int? folderId = null)
    {
        try
        {
            var query = _context.Photos.AsQueryable();

            if (folderId.HasValue)
            {
                query = query.Where(p => p.PhotoFolderId == folderId.Value);
            }

            return await query.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting photo count");
            return 0;
        }
    }
}
