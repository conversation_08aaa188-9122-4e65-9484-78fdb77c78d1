@page "/"
@using PhotoPickr.Models
@using PhotoPickr.Services
@using PhotoPickr.Components.UI
@inject IPhotoService PhotoService
@inject IExportService ExportService
@inject IJSRuntime JSRuntime
@rendermode InteractiveServer

<PageTitle>Photo Pickr</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-3">
            <div class="sidebar">
                <h2 class="mb-4">
                    <i class="fas fa-camera"></i> Photo Pickr
                </h2>

                <FolderPicker SelectedFolderId="selectedFolderId"
                             OnFolderSelected="OnFolderSelected"
                             OnFoldersChanged="OnFoldersChanged" />

                <hr />

                <div class="filters-section">
                    <h5>Filters</h5>
                    <div class="filter-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" @bind="filterCriteria.ShowSelected"
                                   @bind:after="ApplyFilters" id="showSelected">
                            <label class="form-check-label" for="showSelected">Selected</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" @bind="filterCriteria.ShowUnselected"
                                   @bind:after="ApplyFilters" id="showUnselected">
                            <label class="form-check-label" for="showUnselected">Unselected</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" @bind="filterCriteria.ShowBlurry"
                                   @bind:after="ApplyFilters" id="showBlurry">
                            <label class="form-check-label" for="showBlurry">Blurry</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" @bind="filterCriteria.ShowDuplicates"
                                   @bind:after="ApplyFilters" id="showDuplicates">
                            <label class="form-check-label" for="showDuplicates">Duplicates</label>
                        </div>
                    </div>

                    <div class="filter-group mt-3">
                        <label class="form-label">Minimum Rating</label>
                        <select class="form-select form-select-sm" @bind="filterCriteria.MinRating" @bind:after="ApplyFilters">
                            <option value="0">Any</option>
                            <option value="1">1+ Stars</option>
                            <option value="2">2+ Stars</option>
                            <option value="3">3+ Stars</option>
                            <option value="4">4+ Stars</option>
                            <option value="5">5 Stars</option>
                        </select>
                    </div>

                    <div class="filter-group mt-3">
                        <label class="form-label">Sort By</label>
                        <select class="form-select form-select-sm" @bind="filterCriteria.SortBy" @bind:after="ApplyFilters">
                            <option value="@SortBy.DateTaken">Date Taken</option>
                            <option value="@SortBy.FileName">File Name</option>
                            <option value="@SortBy.DateAdded">Date Added</option>
                            <option value="@SortBy.FileSize">File Size</option>
                            <option value="@SortBy.Rating">Rating</option>
                        </select>
                    </div>
                </div>

                @if (selectedPhotoIds.Any())
                {
                    <hr />
                    <div class="export-section">
                        <h5>Export Selected (@selectedPhotoIds.Count)</h5>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary btn-sm" @onclick="ExportToCsv">
                                <i class="fas fa-file-csv"></i> Export CSV
                            </button>
                            <button class="btn btn-outline-primary btn-sm" @onclick="ExportToJson">
                                <i class="fas fa-file-code"></i> Export JSON
                            </button>
                            <button class="btn btn-outline-primary btn-sm" @onclick="CreateZipArchive">
                                <i class="fas fa-file-archive"></i> Create ZIP
                            </button>
                            <button class="btn btn-outline-primary btn-sm" @onclick="CopyToFolder">
                                <i class="fas fa-copy"></i> Copy to Folder
                            </button>
                        </div>
                    </div>
                }
            </div>
        </div>

        <div class="col-md-9">
            <div class="main-content">
                @if (isLoading)
                {
                    <div class="text-center py-5">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading photos...</p>
                    </div>
                }
                else
                {
                    <PhotoGrid Photos="filteredPhotos"
                              OnPhotoSelected="OnPhotoSelected"
                              OnSelectionChanged="OnSelectionChanged"
                              OnPhotosChanged="LoadPhotos" />
                }
            </div>
        </div>
    </div>
</div>

@if (selectedPhoto != null)
{
    <div class="modal fade show d-block" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@selectedPhoto.DisplayName</h5>
                    <button type="button" class="btn-close" @onclick="ClosePhotoViewer"></button>
                </div>
                <div class="modal-body text-center">
                    <img src="@selectedPhoto.FilePath" alt="@selectedPhoto.DisplayName"
                         style="max-width: 100%; max-height: 70vh; object-fit: contain;" />

                    <div class="photo-details mt-3">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Dimensions:</strong> @selectedPhoto.Width × @selectedPhoto.Height<br />
                                <strong>File Size:</strong> @selectedPhoto.FileSizeFormatted<br />
                                <strong>Date Taken:</strong> @selectedPhoto.DateTaken.ToString("MMM dd, yyyy HH:mm")
                            </div>
                            <div class="col-md-6">
                                @if (!string.IsNullOrEmpty(selectedPhoto.CameraMake) || !string.IsNullOrEmpty(selectedPhoto.CameraModel))
                                {
                                    <strong>Camera:</strong> @selectedPhoto.CameraMake @selectedPhoto.CameraModel<br />
                                }
                                @if (selectedPhoto.FocalLength.HasValue)
                                {
                                    <text><strong>Focal Length:</strong> @selectedPhoto.FocalLength.Value mm<br /></text>
                                }
                                @if (selectedPhoto.Aperture.HasValue)
                                {
                                    <text><strong>Aperture:</strong> f/@(selectedPhoto.Aperture.Value)<br /></text>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}

@code {
    private int? selectedFolderId;
    private List<Photo> allPhotos = new();
    private List<Photo> filteredPhotos = new();
    private List<int> selectedPhotoIds = new();
    private Photo? selectedPhoto;
    private FilterCriteria filterCriteria = new();
    private bool isLoading = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadPhotos();
    }

    private async Task OnFolderSelected(int folderId)
    {
        selectedFolderId = folderId == 0 ? null : folderId;
        await LoadPhotos();
    }

    private async Task OnFoldersChanged()
    {
        await LoadPhotos();
    }

    private async Task LoadPhotos()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            allPhotos = await PhotoService.GetPhotosAsync(selectedFolderId);
            await ApplyFilters();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading photos: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task ApplyFilters()
    {
        try
        {
            filteredPhotos = await PhotoService.GetFilteredPhotosAsync(filterCriteria);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error applying filters: {ex.Message}");
        }
    }

    private Task OnPhotoSelected(Photo photo)
    {
        selectedPhoto = photo;
        StateHasChanged();
        return Task.CompletedTask;
    }

    private void ClosePhotoViewer()
    {
        selectedPhoto = null;
        StateHasChanged();
    }

    private Task OnSelectionChanged(List<int> newSelection)
    {
        selectedPhotoIds = newSelection;
        StateHasChanged();
        return Task.CompletedTask;
    }

    private async Task ExportToCsv()
    {
        try
        {
            var selectedPhotos = allPhotos.Where(p => selectedPhotoIds.Contains(p.Id)).ToList();
            if (!selectedPhotos.Any())
            {
                await JSRuntime.InvokeVoidAsync("alert", "No photos selected for export.");
                return;
            }

            var fileName = $"photos_export_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
            var filePath = await ExportService.ExportToCsvAsync(selectedPhotos, fileName);

            if (!string.IsNullOrEmpty(filePath))
            {
                await JSRuntime.InvokeVoidAsync("alert", $"CSV export completed: {fileName}");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Export failed. Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error exporting to CSV: {ex.Message}");
        }
    }

    private async Task ExportToJson()
    {
        try
        {
            var selectedPhotos = allPhotos.Where(p => selectedPhotoIds.Contains(p.Id)).ToList();
            if (!selectedPhotos.Any())
            {
                await JSRuntime.InvokeVoidAsync("alert", "No photos selected for export.");
                return;
            }

            var fileName = $"photos_export_{DateTime.Now:yyyyMMdd_HHmmss}.json";
            var filePath = await ExportService.ExportToJsonAsync(selectedPhotos, fileName);

            if (!string.IsNullOrEmpty(filePath))
            {
                await JSRuntime.InvokeVoidAsync("alert", $"JSON export completed: {fileName}");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Export failed. Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error exporting to JSON: {ex.Message}");
        }
    }

    private async Task CreateZipArchive()
    {
        try
        {
            var selectedPhotos = allPhotos.Where(p => selectedPhotoIds.Contains(p.Id)).ToList();
            if (!selectedPhotos.Any())
            {
                await JSRuntime.InvokeVoidAsync("alert", "No photos selected for export.");
                return;
            }

            var fileName = $"photos_archive_{DateTime.Now:yyyyMMdd_HHmmss}.zip";
            var filePath = await ExportService.CreateZipArchiveAsync(selectedPhotos, fileName);

            if (!string.IsNullOrEmpty(filePath))
            {
                await JSRuntime.InvokeVoidAsync("alert", $"ZIP archive created: {fileName}");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Archive creation failed. Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error creating ZIP archive: {ex.Message}");
        }
    }

    private async Task CopyToFolder()
    {
        try
        {
            var selectedPhotos = allPhotos.Where(p => selectedPhotoIds.Contains(p.Id)).ToList();
            if (!selectedPhotos.Any())
            {
                await JSRuntime.InvokeVoidAsync("alert", "No photos selected for export.");
                return;
            }

            var destinationFolder = await JSRuntime.InvokeAsync<string>("prompt", "Enter destination folder path:");
            if (string.IsNullOrEmpty(destinationFolder))
                return;

            var result = await ExportService.CopySelectedPhotosAsync(selectedPhotos, destinationFolder);

            if (!string.IsNullOrEmpty(result))
            {
                await JSRuntime.InvokeVoidAsync("alert", $"Photos copied to: {destinationFolder}");
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Copy operation failed. Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error copying photos: {ex.Message}");
        }
    }
}
