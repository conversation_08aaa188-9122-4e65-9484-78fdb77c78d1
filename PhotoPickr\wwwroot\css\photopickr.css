/* Photo Pickr Custom Styles */

/* Sidebar */
.sidebar {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    height: calc(100vh - 40px);
    overflow-y: auto;
}

/* Folder Picker */
.folder-picker {
    margin-bottom: 20px;
}

.folder-list {
    max-height: 300px;
    overflow-y: auto;
}

.folder-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.folder-item:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.folder-item.selected {
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.folder-info {
    flex: 1;
}

.folder-name {
    font-weight: 500;
    margin-bottom: 4px;
}

.folder-name i {
    margin-right: 8px;
    color: #6c757d;
}

.folder-path {
    font-size: 0.85em;
    word-break: break-all;
}

.folder-stats {
    margin-top: 4px;
}

.folder-actions {
    display: flex;
    gap: 4px;
}

/* Photo Grid */
.photo-grid-container {
    padding: 20px;
}

.photo-grid {
    display: grid;
    gap: 16px;
    margin-top: 20px;
}

.photo-item {
    position: relative;
    border: 2px solid transparent;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.photo-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.photo-item.selected {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.photo-thumbnail {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
}

.photo-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s ease;
}

.photo-item:hover .photo-thumbnail img {
    transform: scale(1.05);
}

.thumbnail-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    color: #6c757d;
    font-size: 2rem;
}

.photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, transparent 30%, transparent 70%, rgba(0,0,0,0.7) 100%);
    opacity: 0;
    transition: opacity 0.2s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 8px;
}

.photo-item:hover .photo-overlay {
    opacity: 1;
}

.photo-selection {
    align-self: flex-start;
}

.photo-selection input[type="checkbox"] {
    transform: scale(1.2);
}

.photo-rating {
    align-self: flex-end;
    display: flex;
    gap: 2px;
}

.photo-rating i {
    color: #ffc107;
    cursor: pointer;
    transition: transform 0.1s ease;
}

.photo-rating i:hover {
    transform: scale(1.2);
}

.photo-badges {
    align-self: flex-start;
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.photo-badges .badge {
    font-size: 0.7rem;
}

.photo-info {
    padding: 8px;
}

.photo-name {
    font-weight: 500;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.photo-details {
    margin-top: 4px;
    line-height: 1.3;
}

/* Filters */
.filters-section {
    margin-bottom: 20px;
}

.filter-group {
    margin-bottom: 12px;
}

.filter-group .form-check {
    margin-bottom: 4px;
}

/* Export Section */
.export-section {
    margin-top: 20px;
}

/* Main Content */
.main-content {
    padding: 20px;
    height: calc(100vh - 40px);
    overflow-y: auto;
}

/* Photo Grid Header */
.photo-grid-header {
    padding: 16px 0;
    border-bottom: 1px solid #dee2e6;
}

.photo-count .badge {
    font-size: 0.9rem;
}

.grid-controls .btn-group .btn {
    padding: 4px 8px;
}

/* Modal Customizations */
.modal-xl {
    max-width: 90vw;
}

.modal-body img {
    border-radius: 4px;
}

.photo-details {
    text-align: left;
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin-top: 16px;
}

/* Scanning Progress */
.scanning-progress {
    position: sticky;
    bottom: 0;
    z-index: 10;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        height: auto;
        margin-bottom: 20px;
    }
    
    .main-content {
        height: auto;
    }
    
    .photo-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
    }
    
    .folder-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .folder-actions {
        margin-top: 8px;
        align-self: flex-end;
    }
}

/* Loading States */
.spinner-border {
    color: #007bff;
}

/* Utility Classes */
.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Custom Scrollbars */
.sidebar::-webkit-scrollbar,
.main-content::-webkit-scrollbar,
.folder-list::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track,
.main-content::-webkit-scrollbar-track,
.folder-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb,
.main-content::-webkit-scrollbar-thumb,
.folder-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover,
.main-content::-webkit-scrollbar-thumb:hover,
.folder-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
