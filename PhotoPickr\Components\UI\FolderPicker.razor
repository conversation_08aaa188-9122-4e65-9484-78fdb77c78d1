@using PhotoPickr.Models
@using PhotoPickr.Services
@inject IFolderService FolderService
@inject IPhotoService PhotoService
@inject IJSRuntime JSRuntime

<div class="folder-picker">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h4>Photo Folders</h4>
        <button class="btn btn-primary" @onclick="ShowAddFolderDialog">
            <i class="fas fa-plus"></i> Add Folder
        </button>
    </div>
    
    @if (folders.Any())
    {
        <div class="folder-list">
            @foreach (var folder in folders)
            {
                <div class="folder-item @(folder.Id == SelectedFolderId ? "selected" : "")" 
                     @onclick="() => SelectFolder(folder.Id)">
                    <div class="folder-info">
                        <div class="folder-name">
                            <i class="fas fa-folder"></i>
                            @folder.DisplayName
                        </div>
                        <div class="folder-path text-muted">@folder.FolderPath</div>
                        <div class="folder-stats">
                            <small class="text-muted">
                                @folder.PhotoCount photos
                                @if (folder.LastScanned != DateTime.MinValue)
                                {
                                    <span> • Last scanned: @folder.LastScanned.ToString("MMM dd, yyyy")</span>
                                }
                                @if (!folder.Exists)
                                {
                                    <span class="text-danger"> • Folder not found</span>
                                }
                            </small>
                        </div>
                    </div>
                    <div class="folder-actions">
                        <button class="btn btn-sm btn-outline-primary" @onclick="() => ScanFolder(folder.Id)" 
                                @onclick:stopPropagation="true" disabled="@isScanning">
                            @if (isScanning && scanningFolderId == folder.Id)
                            {
                                <i class="fas fa-spinner fa-spin"></i>
                            }
                            else
                            {
                                <i class="fas fa-sync"></i>
                            }
                            Scan
                        </button>
                        <button class="btn btn-sm btn-outline-danger" @onclick="() => RemoveFolder(folder.Id)" 
                                @onclick:stopPropagation="true">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center text-muted py-4">
            <i class="fas fa-folder-open fa-3x mb-3"></i>
            <p>No folders added yet. Click "Add Folder" to get started.</p>
        </div>
    }
    
    @if (showAddDialog)
    {
        <div class="modal fade show d-block" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Add Photo Folder</h5>
                        <button type="button" class="btn-close" @onclick="HideAddFolderDialog"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Folder Path</label>
                            <div class="input-group">
                                <input type="text" class="form-control" @bind="newFolderPath" 
                                       placeholder="Enter folder path or click Browse...">
                                <button class="btn btn-outline-secondary" @onclick="BrowseForFolder">
                                    Browse...
                                </button>
                            </div>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" @bind="includeSubfolders" id="includeSubfolders">
                            <label class="form-check-label" for="includeSubfolders">
                                Include subfolders (recursive scan)
                            </label>
                        </div>
                        @if (!string.IsNullOrEmpty(addFolderError))
                        {
                            <div class="alert alert-danger mt-3">@addFolderError</div>
                        }
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @onclick="HideAddFolderDialog">Cancel</button>
                        <button type="button" class="btn btn-primary" @onclick="AddFolder" disabled="@string.IsNullOrEmpty(newFolderPath)">
                            Add Folder
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-backdrop fade show"></div>
    }
    
    @if (isScanning)
    {
        <div class="scanning-progress mt-3">
            <div class="alert alert-info">
                <i class="fas fa-spinner fa-spin"></i>
                @scanProgress
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public int? SelectedFolderId { get; set; }
    [Parameter] public EventCallback<int> OnFolderSelected { get; set; }
    [Parameter] public EventCallback OnFoldersChanged { get; set; }
    
    private List<PhotoFolder> folders = new();
    private bool showAddDialog = false;
    private string newFolderPath = "";
    private bool includeSubfolders = true;
    private string addFolderError = "";
    private bool isScanning = false;
    private int? scanningFolderId = null;
    private string scanProgress = "";
    
    protected override async Task OnInitializedAsync()
    {
        await LoadFolders();
    }
    
    private async Task LoadFolders()
    {
        folders = await FolderService.GetAllFoldersAsync();
        StateHasChanged();
    }
    
    private async Task SelectFolder(int folderId)
    {
        SelectedFolderId = folderId;
        await OnFolderSelected.InvokeAsync(folderId);
    }
    
    private void ShowAddFolderDialog()
    {
        showAddDialog = true;
        newFolderPath = "";
        addFolderError = "";
        includeSubfolders = true;
    }
    
    private void HideAddFolderDialog()
    {
        showAddDialog = false;
        newFolderPath = "";
        addFolderError = "";
    }
    
    private async Task BrowseForFolder()
    {
        try
        {
            // Note: This would require a JavaScript interop to show a folder picker dialog
            // For now, we'll just show an alert
            await JSRuntime.InvokeVoidAsync("alert", "Please enter the folder path manually. Folder browser dialog requires additional JavaScript implementation.");
        }
        catch (Exception ex)
        {
            addFolderError = $"Error browsing for folder: {ex.Message}";
        }
    }
    
    private async Task AddFolder()
    {
        try
        {
            addFolderError = "";
            
            if (string.IsNullOrWhiteSpace(newFolderPath))
            {
                addFolderError = "Please enter a folder path.";
                return;
            }
            
            if (!Directory.Exists(newFolderPath))
            {
                addFolderError = "The specified folder does not exist.";
                return;
            }
            
            if (!FolderService.IsValidImageFolder(newFolderPath))
            {
                addFolderError = "The specified folder does not contain any image files.";
                return;
            }
            
            await FolderService.AddFolderAsync(newFolderPath, includeSubfolders);
            await LoadFolders();
            await OnFoldersChanged.InvokeAsync();
            HideAddFolderDialog();
        }
        catch (Exception ex)
        {
            addFolderError = $"Error adding folder: {ex.Message}";
        }
    }
    
    private async Task RemoveFolder(int folderId)
    {
        try
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to remove this folder? This will also remove all associated photo records.");
            if (confirmed)
            {
                await FolderService.RemoveFolderAsync(folderId);
                await LoadFolders();
                await OnFoldersChanged.InvokeAsync();
                
                if (SelectedFolderId == folderId)
                {
                    SelectedFolderId = null;
                    await OnFolderSelected.InvokeAsync(0);
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error removing folder: {ex.Message}");
        }
    }
    
    private async Task ScanFolder(int folderId)
    {
        try
        {
            isScanning = true;
            scanningFolderId = folderId;
            scanProgress = "Starting scan...";
            StateHasChanged();
            
            var progress = new Progress<string>(message =>
            {
                scanProgress = message;
                InvokeAsync(StateHasChanged);
            });
            
            // Note: This is a simplified approach. In a real application, you might want to use a background service
            await PhotoService.ScanFolderAsync(folderId, progress);
            
            await LoadFolders();
            await OnFoldersChanged.InvokeAsync();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"Error scanning folder: {ex.Message}");
        }
        finally
        {
            isScanning = false;
            scanningFolderId = null;
            scanProgress = "";
            StateHasChanged();
        }
    }
}
