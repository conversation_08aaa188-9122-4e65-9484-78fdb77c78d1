using System.ComponentModel.DataAnnotations;

namespace PhotoPickr.Models;

public class PhotoFolder
{
    public int Id { get; set; }
    
    [Required]
    public string FolderPath { get; set; } = string.Empty;
    
    public string FolderName { get; set; } = string.Empty;
    
    public DateTime DateAdded { get; set; } = DateTime.UtcNow;
    
    public DateTime LastScanned { get; set; }
    
    public int PhotoCount { get; set; }
    
    public bool IsRecursive { get; set; } = true;
    
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public ICollection<Photo> Photos { get; set; } = new List<Photo>();
    
    // Computed properties
    public string DisplayName => Path.GetFileName(FolderPath) ?? FolderPath;
    
    public bool Exists => Directory.Exists(FolderPath);
}
