using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using PhotoPickrMAUI.Data;
using PhotoPickrMAUI.Services;

namespace PhotoPickrMAUI;

public static class MauiProgram
{
	public static MauiApp CreateMauiApp()
	{
		var builder = MauiApp.CreateBuilder();
		builder
			.UseMauiApp<App>()
			.ConfigureFonts(fonts =>
			{
				fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
			});

		builder.Services.AddMauiBlazorWebView();

		// Add Entity Framework
		var dbPath = Path.Combine(FileSystem.AppDataDirectory, "photopickr.db");
		builder.Services.AddDbContext<PhotoPickrContext>(options =>
			options.UseSqlite($"Data Source={dbPath}"));

		// Add application services
		builder.Services.AddScoped<IFolderService, FolderService>();
		builder.Services.AddScoped<IPhotoService, PhotoService>();
		builder.Services.AddScoped<IThumbnailService, ThumbnailService>();
		builder.Services.AddScoped<IExportService, ExportService>();
		builder.Services.AddScoped<IFolderPickerService, FolderPickerService>();

#if DEBUG
		builder.Services.AddBlazorWebViewDeveloperTools();
		builder.Logging.AddDebug();
#endif

		var app = builder.Build();

		// Ensure database is created
		using (var scope = app.Services.CreateScope())
		{
			var context = scope.ServiceProvider.GetRequiredService<PhotoPickrContext>();
			context.Database.EnsureCreated();
		}

		return app;
	}
}
